<template>
  <view class="checkin-page">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <view class="nav-content">
        <view class="nav-left" @click="handleBack">
          <uni-icons type="left" size="18" color="#fff" />
        </view>
        <view class="nav-title">工分任务</view>
        <view class="nav-right"></view>
      </view>
    </view>

    <!-- 顶部任务卡片 -->
    <view class="task-header-card">
      <view class="task-left">
        <view class="task-days">
          <text class="days-number">{{ consecutiveDays }}</text>
          <text class="days-text">天</text>
        </view>
        <view class="task-info">
          <view class="task-title">每日签到获奖励</view>
          <view class="task-desc"
            >连续签到{{ totalDays }}天可获得{{ totalReward }}个工分</view
          >
        </view>
      </view>
      <view class="task-right">
        <view class="gift-icon">🎁</view>
        <button
          class="checkin-btn"
          :class="{ disabled: hasCheckedIn }"
          @click="handleCheckin"
          :disabled="hasCheckedIn"
        >
          {{ hasCheckedIn ? "已签到" : "签到" }}
        </button>
      </view>

      <!-- 月份切换 - 在卡片底部 -->
      <view class="month-tabs">
        <view
          class="month-tab"
          :class="{ active: currentMonthIndex === 0 }"
          @click="setMonth(0)"
        >
          2024/07
        </view>
        <view
          class="month-tab"
          :class="{ active: currentMonthIndex === 1 }"
          @click="setMonth(1)"
        >
          2024/08
        </view>
        <view
          class="month-tab"
          :class="{ active: currentMonthIndex === 2 }"
          @click="setMonth(2)"
        >
          2024/09
        </view>
      </view>
    </view>

    <!-- 日历组件 -->
    <view class="calendar-section">
      <swiper
        class="calendar-swiper"
        :current="currentMonthIndex"
        @change="onSwiperChange"
        :duration="300"
      >
        <swiper-item v-for="(monthData, index) in monthsData" :key="index">
          <view class="calendar-grid">
            <!-- 星期标题 -->
            <view class="week-header">
              <text v-for="week in weekDays" :key="week" class="week-day">{{
                week
              }}</text>
            </view>

            <!-- 日期网格 -->
            <view class="date-grid">
              <view
                v-for="(date, dateIndex) in monthData.dates"
                :key="dateIndex"
                class="date-item"
                :class="{
                  'other-month': date.isOtherMonth,
                  today: date.isToday,
                  checked: date.isChecked,
                  'past-unchecked': date.isPastUnchecked,
                  'current-month': !date.isOtherMonth,
                }"
              >
                <view class="date-content">
                  <text class="date-number">{{ date.day }}</text>
                  <text
                    v-if="date.isChecked && !date.isOtherMonth"
                    class="status-text"
                    >已签</text
                  >
                  <text
                    v-else-if="date.isPastUnchecked && !date.isOtherMonth"
                    class="status-text"
                    >未签</text
                  >
                </view>
              </view>
            </view>
          </view>
        </swiper-item>
      </swiper>
    </view>

    <!-- 签到规则说明 -->
    <view class="rules-section">
      <view class="rules-title">签到规则</view>
      <view class="rules-list">
        <text class="rule-item"
          >1.
          每日普通签到可获得1个工分，连续签到（两周）达到14天可获得10个工分。</text
        >
        <text class="rule-item">2. 签到任务每天可完成1次，从0点开始计算。</text>
        <text class="rule-item"
          >3. 连续签到中断后，需要重新开始计算连续天数。</text
        >
        <text class="rule-item"
          >4.
          在有网络（月网网络、月网网络）计算过程中，出现一次未签到，将会从额外工分奖励，一个月共计算次数外奖励过1分钟会。</text
        >
        <text class="rule-item"
          >5.
          工分币可以用作商品兑换，兑换规则详情工分商城产品兑换说明，本规则最终解释权归生活助手所有。</text
        >
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue"

// 响应式数据
const consecutiveDays = ref(3) // 连续签到天数
const totalDays = ref(14) // 总目标天数
const totalReward = ref(10) // 总奖励工分
const hasCheckedIn = ref(false) // 今日是否已签到
const currentDate = ref(new Date())
const currentMonthIndex = ref(6) // 当前选中的月份索引，默认选中当前月（数组中间位置）
const checkedDates = ref<string[]>([
  "2024-08-06",
  "2024-08-07",
  "2024-08-08",
  "2024-08-09",
  "2024-08-10",
  "2024-08-12",
  "2024-08-13",
  "2024-08-14",
  "2024-08-15",
  "2024-08-16",
]) // 已签到日期

// 星期标题
const weekDays = ["日", "一", "二", "三", "四", "五", "六"]

// 获取当前日期
const today = new Date()
const currentYear = today.getFullYear()
const currentMonth = today.getMonth() // 0-11

// 生成单个月份的日历数据
const generateMonthData = (year: number, month: number) => {
  const today = new Date()

  // 获取当月第一天和最后一天
  const firstDay = new Date(year, month, 1)
  const lastDay = new Date(year, month + 1, 0)

  // 获取第一天是星期几
  const firstDayWeek = firstDay.getDay()

  const dates = []

  // 添加上个月的日期（填充）
  const prevMonth = new Date(year, month - 1, 0)
  for (let i = firstDayWeek - 1; i >= 0; i--) {
    const day = prevMonth.getDate() - i
    dates.push({
      day,
      isOtherMonth: true,
      isToday: false,
      isChecked: false,
      isPastUnchecked: false,
    })
  }

  // 添加当月日期
  for (let day = 1; day <= lastDay.getDate(); day++) {
    const dateStr = `${year}-${String(month + 1).padStart(2, "0")}-${String(
      day
    ).padStart(2, "0")}`
    const isToday =
      year === today.getFullYear() &&
      month === today.getMonth() &&
      day === today.getDate()

    const currentDateObj = new Date(year, month, day)
    const isChecked = checkedDates.value.includes(dateStr)
    const isPastUnchecked = currentDateObj < today && !isChecked && !isToday

    dates.push({
      day,
      isOtherMonth: false,
      isToday,
      isChecked,
      isPastUnchecked,
    })
  }

  // 添加下个月的日期（填充到42个格子）
  const remainingDays = 42 - dates.length
  for (let day = 1; day <= remainingDays; day++) {
    dates.push({
      day,
      isOtherMonth: true,
      isToday: false,
      isChecked: false,
      isPastUnchecked: false,
    })
  }

  return dates
}

// 生成所有月份的日历数据（前后各6个月，共13个月）
const monthsData = computed(() => {
  const months = []
  const baseYear = currentYear
  const baseMonth = currentMonth

  // 生成前后各6个月的数据
  for (let i = -6; i <= 6; i++) {
    const targetDate = new Date(baseYear, baseMonth + i, 1)
    months.push({
      year: targetDate.getFullYear(),
      month: targetDate.getMonth(),
      dates: generateMonthData(targetDate.getFullYear(), targetDate.getMonth()),
    })
  }

  return months
})

// 动态生成头部显示的月份标签
const displayMonths = computed(() => {
  const currentIndex = currentMonthIndex.value
  const months = monthsData.value

  if (months.length === 0) return []

  const result = []
  for (let i = -1; i <= 1; i++) {
    const index = currentIndex + i
    if (index >= 0 && index < months.length) {
      const monthData = months[index]
      const year = monthData.year
      const month = monthData.month + 1 // 显示时月份+1
      result.push({
        index: index,
        label: `${year}/${String(month).padStart(2, "0")}`,
        isActive: i === 0,
      })
    }
  }

  return result
})

// 方法
const handleBack = () => {
  uni.navigateBack()
}

const handleCheckin = async () => {
  if (hasCheckedIn.value) return

  try {
    // 这里调用签到API
    // const res = await checkinAPI();

    // 模拟签到成功
    hasCheckedIn.value = true
    consecutiveDays.value += 1

    // 添加今日签到记录
    const today = new Date()
    const todayStr = `${today.getFullYear()}-${String(
      today.getMonth() + 1
    ).padStart(2, "0")}-${String(today.getDate()).padStart(2, "0")}`
    checkedDates.value.push(todayStr)

    uni.showToast({
      title: "签到成功！获得1工分",
      icon: "success",
      duration: 2000,
    })
  } catch (error) {
    uni.showToast({
      title: "签到失败，请重试",
      icon: "none",
    })
  }
}

const setMonth = (index: number) => {
  if (index >= 0 && index < monthsData.value.length) {
    currentMonthIndex.value = index
    const selectedMonth = monthsData.value[index]
    currentDate.value = new Date(selectedMonth.year, selectedMonth.month, 1)
  }
}

// swiper切换事件
const onSwiperChange = (e: any) => {
  const current = e.detail.current
  setMonth(current)
}

// 页面初始化
onMounted(() => {
  // 设置默认显示当前月份（数组中间位置）
  setMonth(6)

  // 检查今日是否已签到
  const today = new Date()
  const todayStr = `${today.getFullYear()}-${String(
    today.getMonth() + 1
  ).padStart(2, "0")}-${String(today.getDate()).padStart(2, "0")}`
  hasCheckedIn.value = checkedDates.value.includes(todayStr)
})
</script>

<style lang="scss" scoped>
.checkin-page {
  min-height: 100vh;
  background: #f5f5f5;
}

.nav-bar {
  background: linear-gradient(135deg, #086bdb 0%, #4a9eff 100%);
  padding-top: var(--status-bar-height);

  .nav-content {
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24rpx;

    .nav-left {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .nav-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #fff;
    }

    .nav-right {
      width: 60rpx;
    }
  }
}

.task-header-card {
  background: linear-gradient(135deg, #086bdb 0%, #4a9eff 100%);
  margin: 24rpx;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(8, 107, 219, 0.3);
  position: relative;

  .task-left {
    display: flex;
    align-items: flex-start;
    margin-bottom: 32rpx;
    padding-right: 120rpx;

    .task-days {
      display: flex;
      align-items: baseline;
      margin-right: 32rpx;
      flex-shrink: 0;

      .days-number {
        font-size: 80rpx;
        font-weight: bold;
        color: #fff;
        line-height: 1;
      }

      .days-text {
        font-size: 36rpx;
        color: #fff;
        margin-left: 8rpx;
        font-weight: 500;
      }
    }

    .task-info {
      flex: 1;
      padding-top: 8rpx;

      .task-title {
        font-size: 34rpx;
        color: #fff;
        font-weight: bold;
        margin-bottom: 12rpx;
        line-height: 1.2;
      }

      .task-desc {
        font-size: 28rpx;
        color: rgba(255, 255, 255, 0.9);
        line-height: 1.4;
      }
    }
  }

  .task-right {
    position: absolute;
    top: 32rpx;
    right: 32rpx;
    display: flex;
    flex-direction: column;
    align-items: center;

    .gift-icon {
      font-size: 52rpx;
      margin-bottom: 16rpx;
    }

    .checkin-btn {
      background: #ff6b35;
      color: #fff;
      font-size: 28rpx;
      font-weight: bold;
      border-radius: 28rpx;
      padding: 0 28rpx;
      height: 56rpx;
      line-height: 56rpx;
      border: none;
      box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.4);
      min-width: 80rpx;

      &.disabled {
        background: rgba(255, 255, 255, 0.25);
        color: rgba(255, 255, 255, 0.9);
        box-shadow: none;
      }
    }
  }

  .month-tabs {
    display: flex;
    background: rgba(255, 255, 255, 0.12);
    border-radius: 24rpx;
    padding: 8rpx;
    gap: 6rpx;
    margin-top: 8rpx;

    .month-tab {
      flex: 1;
      text-align: center;
      padding: 16rpx 0;
      font-size: 28rpx;
      color: rgba(255, 255, 255, 0.75);
      border-radius: 18rpx;
      transition: all 0.3s ease;
      font-weight: 500;

      &.active {
        background: rgba(255, 255, 255, 0.2);
        color: #fff;
        font-weight: bold;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
        transform: scale(1.02);
      }
    }
  }
}

.calendar-section {
  background: #fff;
  margin: 0 24rpx 24rpx 24rpx;
  border-radius: 16rpx;
  overflow: hidden;

  .calendar-swiper {
    height: 600rpx;
  }

  .calendar-grid {
    padding: 32rpx 24rpx 24rpx 24rpx;

    .week-header {
      display: flex;
      margin-bottom: 24rpx;

      .week-day {
        flex: 1;
        text-align: center;
        font-size: 28rpx;
        color: #666;
        font-weight: 500;
      }
    }

    .date-grid {
      display: flex;
      flex-wrap: wrap;

      .date-item {
        width: calc(100% / 7);
        height: 100rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;

        .date-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          width: 64rpx;
          height: 80rpx;
        }

        .date-number {
          font-size: 30rpx;
          color: #333;
          font-weight: 500;
          line-height: 1;
          margin-bottom: 4rpx;
        }

        .status-text {
          font-size: 20rpx;
          line-height: 1;
        }

        &.other-month {
          .date-number {
            color: #ccc;
          }
        }

        &.today {
          .date-number {
            color: #086bdb;
            font-weight: bold;
          }
        }

        &.checked {
          .date-content {
            background: #086bdb;
            border-radius: 50%;
            width: 64rpx;
            height: 64rpx;
            position: relative;
          }

          .date-number {
            color: #fff;
            font-weight: bold;
            font-size: 24rpx;
            margin-bottom: 0;
          }

          .status-text {
            color: #fff;
            font-size: 18rpx;
            position: absolute;
            bottom: 8rpx;
            left: 50%;
            transform: translateX(-50%);
          }
        }

        &.past-unchecked {
          .date-content {
            border: 2rpx solid #e5e5e5;
            border-radius: 50%;
            width: 64rpx;
            height: 64rpx;
            position: relative;
          }

          .date-number {
            color: #ccc;
            font-size: 24rpx;
            margin-bottom: 0;
          }

          .status-text {
            color: #ccc;
            font-size: 18rpx;
            position: absolute;
            bottom: 8rpx;
            left: 50%;
            transform: translateX(-50%);
          }
        }
      }
    }
  }
}

.rules-section {
  background: #fff;
  margin: 24rpx;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);

  .rules-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 24rpx;
  }

  .rules-list {
    .rule-item {
      display: block;
      font-size: 28rpx;
      color: #666;
      line-height: 1.8;
      margin-bottom: 20rpx;
      text-align: justify;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>
